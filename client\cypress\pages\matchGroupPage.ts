import {
  MatchGroupDataTestId,
  MatchGroupTab,
  MatchGroupButton,
  MatchGroupText,
} from '../support/helperFunction/matchGroupHelper';

export const matchGroupPage = {
  verifyMatchGroupPageUI: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP }).should('be.visible');
  },

  verifyBreadcrumb: (eventName: string, groupName: string, potentialMatchSearchName?: string) => {
    cy.get('[aria-label="breadcrumb"]').should('be.visible');
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.EVENT_BREADCRUMB }).should('contain.text', eventName);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_BREADCRUMB }).should('contain.text', groupName);
    if (potentialMatchSearchName) {
      cy.getDataIdCy({ idAlias: MatchGroupDataTestId.POTENTIAL_MATCH_SEARCH_BREADCRUMB }).should('contain.text', potentialMatchSearchName);
    }
    cy.contains(MatchGroupText.ALL_EVENTS).should('be.visible');
  },

  verifyFileNameDropdownAndExportButton: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FILE_AND_FILTER_MATCHES_FILTER_SELECT }).should('be.visible');
  },

  verifyLeftScreenContent: () => {
    cy.contains(MatchGroupText.SELECT_TRACKLET).should('be.visible');
  },

  verifyRightScreenTabs: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TABS }).should('be.visible');
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_SEARCH }).should('contain.text', MatchGroupTab.SEARCH_RESULTS);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_DETAIL }).should('contain.text', MatchGroupTab.VERIFIED_MATCHES);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_TIMELINE }).should('contain.text', MatchGroupTab.TIMELINE_EDITOR);
  },

  verifyDefaultTab: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_SEARCH }).should('have.class', 'Mui-selected');
  },

  clickTab: (tabName: string) => {
    let tabId;
    switch (tabName) {
    case MatchGroupTab.SEARCH_RESULTS:
      tabId = MatchGroupDataTestId.MATCH_GROUP_TAB_SEARCH;
      break;
    case MatchGroupTab.VERIFIED_MATCHES:
      tabId = MatchGroupDataTestId.MATCH_GROUP_TAB_DETAIL;
      break;
    case MatchGroupTab.TIMELINE_EDITOR:
      tabId = MatchGroupDataTestId.MATCH_GROUP_TAB_TIMELINE;
      break;
    default:
      throw new Error(`Invalid tab name: ${tabName}`);
    }
    cy.getDataIdCy({ idAlias: tabId }).click();
  },

  selectPotentialMatchSearch: (selection: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.POTENTIAL_MATCH_SEARCH_DROP }).click();
    cy.get('[role="listbox"]').contains(selection).click();
  },

  hoverOnMatchGroupSearch: (searchName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, searchName).trigger('mouseover');
  },

  clickEditIcon: (searchId: string) => {
    cy.get(`[data-testid="${MatchGroupDataTestId.MATCHGROUP_EDIT}${searchId}"]`).click();
  },

  clickDeleteIcon: (searchId: string) => {
    cy.get(`[data-testid="${MatchGroupDataTestId.MATCHGROUP_DELETE}${searchId}"]`).click();
  },

  hoverOnFirstMatchGroupSearch: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`).first().trigger('mouseover');
  },

  clickFirstEditIcon: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCHGROUP_EDIT}"]`).first().click();
  },

  clickFirstDeleteIcon: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCHGROUP_DELETE}"]`).first().click();
  },

  enterNewSearchName: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).clear();
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).type(newName);
  },

  clickSaveInDialog: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.SAVE).click();
  },

  clickDeleteInDialog: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.DELETE).click();
  },

  verifySearchNameUpdated: (newName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, newName).should('be.visible');
  },

  verifySearchDeleted: (searchName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, searchName).should('not.exist');
  },

  clickThreeDotsButton: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_ICON}"]`).first().click();
  },

  selectRename: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_RENAME}"]`).click();
  },

  selectDelete: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_DELETE}"]`).click();
  },

  enterNewMatchGroupName: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).clear();
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).type(newName);
  },

  clickYesDelete: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.YES_DELETE).click();
  },

  verifyDeleteConfirmationModal: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).should('be.visible');
    cy.contains(MatchGroupText.DELETE_CONFIRMATION).should('be.visible');
    cy.contains('button', MatchGroupButton.CANCEL).should('be.visible');
    cy.contains('button', MatchGroupButton.YES_DELETE).should('be.visible');
  },

  verifyMatchGroupDeleted: () => {
    cy.url().should('not.include', '/match-group/');
  },

  verifyMatchGroupNameUpdated: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_BREADCRUMB }).should('contain.text', newName);
  },

  clickFirstViewMatchGroupButton: () => {
    cy.get('[data-testid^="match-group-row-"]').first().within(() => {
      cy.contains('button', MatchGroupButton.VIEW_MATCH_GROUP).click();
    });
  },
  verifyMatchSearchViewPage: () => {
    cy.url().should('include', '/potential-match-search/');
  },
  verifyPageIsLoaded: () => {
    cy.get('[class*="Skeleton"]').should('be.visible');
    cy.get('[class*="Skeleton"]').should('not.exist');
  },
  verifyBreadcrumbIsVisible: () => {
    cy.get('nav[aria-label="breadcrumb"]').should('be.visible');
  },

  verifyFileNameDropdown: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FILE_AND_FILTER_MATCHES_FILTER_SELECT }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyMarkAsVerifiedButton: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FILE_AND_FILTER_MATCHES__ADD_TO_BUTTON }).should(state === 'disabled' ? 'be.disabled' : 'not.be.disabled');
  },

  verifyFindMatchesButton: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FILE_AND_FILTER_MATCHES__DETAIL_UPLOAD_BUTTON }).should(state === 'disabled' ? 'be.disabled' : 'not.be.disabled');
  },

  verifyPotentialMatchSearchDropdown: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.POTENTIAL_MATCH_SEARCH_DROP }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyMediaPlayerDefaultText: (state: string) => {
    cy.contains('Select a Detection to View Details.').should(state === 'visible' ? 'be.visible' : 'not.exist');
  },

  verifySelectAllCheckbox: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.TRACKLET_SELECTION_HEADER_SELECT_ALL }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyConfidenceSlider: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.SEARCH_RESULTS_CONFIDENCE_SLIDER }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyShowMatchScoreCheckbox: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.SEARCH_RESULTS_CONFIDENCE_CHECKBOX }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyPagination: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.PAGINATION }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },

  verifyThumbnailScaler: (state: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.THUMBNAIL_SCALER }).should(state === 'visible' ? 'be.visible' : 'not.be.visible');
  },
  selectTrackletByIndex: (index: number) => {
    cy.getDataIdCy({ idAlias: `Tracklet-${index}-checkbox` }).click();
  },

  clickFindMatchesButton: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FILE_AND_FILTER_MATCHES__DETAIL_UPLOAD_BUTTON }).should('be.enabled').click();
  },

  verifyFindMatchesPopoverIsVisible: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FIND_MATCHES_POPOVER }).should('be.visible');
  },

  clickNewMatchGroupInPopover: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FIND_MATCHES_POPOVER_NEW_MATCH_ADD }).click();
  },

  enterNewMatchGroupNameInPopover: (name: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FIND_MATCHES_POPOVER }).find('input[type="text"]').type(name);
  },

  clickCreateNewMatchGroupInPopover: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FIND_MATCHES_POPOVER_NEW_MATCH_CONFIRM }).click();
  },

  clickContinueInPopover: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.FIND_MATCHES_POPOVER_CONFIRM }).click();
  },

  verifySuccessNotification: (message: string) => {
    cy.getDataIdCy({ idAlias: 'snackbar' }).contains(message).should('be.visible');
  },
  clickOnTrackletByIndex: (index: number) => {
    cy.getDataIdCy({ idAlias: `Tracklet-${index}-img` }).click();
  },

  verifyAccordionIsVisible: (accordionName: string) => {
    cy.contains('[role="button"]', accordionName).should('be.visible');
  },
  selectTrackletCheckboxByIndex: (index: number) => {
    cy.getDataIdCy({ idAlias: `Tracklet-${index}-checkbox` }).click();
  },

  verifyTrackletHasBlueOutline: (index: number) => {
    cy.getDataIdCy({ idAlias: `Tracklet-${index}` })
      .children()
      .first()
      .should('have.css', 'border-style', 'solid')
      .and('have.css', 'border-color', 'rgb(39, 140, 255)');
  },
  verifyVideoPlayerState: (state: 'playing' | 'paused') => {
    const expectedButtonText = state === 'paused' ? 'Play' : 'Pause';
    cy.get('.video-react-control-bar')
      .contains('button', expectedButtonText)
      .should('be.visible');
  },

  clickVideoPlayerButton: (buttonName: 'Play' | 'Pause') => {
    cy.get('.video-react-control-bar')
      .contains('button', buttonName)
      .click();
  },
  verifyConfidenceSliderIsVisible: () => {
    cy.getDataIdCy({ idAlias: 'search-results-confidence-slider' }).should('be.visible');
  },
  clickSelectAllCheckbox: () => {
    cy.getDataIdCy({ idAlias: 'tracklet-selection-header-select-all' }).click();
  },

  verifyAllTrackletsAreSelected: () => {
    cy.getDataIdCy({ idAlias: 'table-pagination-page-selector-text' })
      .invoke('text')
      .then((paginationText) => {
        const parts = paginationText.split(' ');
        const totalItemsOnPage = parts[parts.length - 1];

        cy.getDataIdCy({ idAlias: 'tracklet-selection-header-selected-tracklets-label' })
          .should('be.visible')
          .and('contain.text', `${totalItemsOnPage} selected`);
      });
  },
  clickCheckboxByLabel: (checkboxLabel: string) => {
    cy.contains(checkboxLabel)
      .parent()
      .find('[type="checkbox"]')
      .click();
  },

  verifyAllTrackletsShowConfidenceScore: () => {
    cy.get('div[data-testid^="Tracklet-"]').each(($trackletContainer) => {
      cy.wrap($trackletContainer)
        .parent()
        .find('[data-testid$="-confidence"]')
        .should('be.visible');
    });
  },
  verifyFindMatchesButtonState: (state: 'enabled' | 'disabled') => {
    cy.getDataIdCy({ idAlias: 'file-and-filter-matches__detail-upload-button' })
      .should(state === 'enabled' ? 'be.enabled' : 'be.disabled');
  },
  filterByFileName: (fileName: string) => {
    cy.getDataIdCy({ idAlias: 'table-pagination-page-selector-text' })
      .invoke('text')
      .as('initialPaginationText');
    cy.getDataIdCy({ idAlias: 'file-and-filter-matches-filter-select' }).click();
    cy.get('[role="option"]').contains(fileName).click();
    cy.get('body').click();
    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: 'table-pagination-page-selector-text' })
        .should('not.have.text', initialText);
    });
  },

  verifyAllTrackletsAreFromSameFile: () => {
    cy.get('img[data-testid$="-img"]').then(($images) => {
      if ($images.length === 0) {
        return;
      }
      const fileIds = $images.map((_, el) => Cypress.$(el).attr('data-file-id')).get();
      const uniqueFileIds = new Set(fileIds);
      expect(uniqueFileIds.size).to.equal(2);
    });
  },
  selectFromPotentialMatchSearchDropdown: (searchName: string) => {
    cy.getDataIdCy({ idAlias: 'table-pagination-page-selector-text' })
      .invoke('text')
      .as('initialPaginationText');

    cy.getDataIdCy({ idAlias: 'potential-match-search-drop' }).click();
    cy.get('[role="option"]').contains(searchName).click();

    cy.get('@initialPaginationText').then((initialText) => {
      cy.getDataIdCy({ idAlias: 'table-pagination-page-selector-text' })
        .should('not.have.text', initialText);
    });
  },

  verifySelectedPotentialMatchSearch: (searchName: string) => {
    cy.getDataIdCy({ idAlias: 'potential-match-search-drop' })
      .should('contain.text', searchName);
  },
  verifySearchModalContainsOptions: (option1: string, option2: string) => {
    cy.get('[role="radiogroup"]').contains(option1).should('be.visible');
    cy.get('[role="radiogroup"]').contains(option2).should('be.visible');
  },

  verifySearchModalContainsButtons: (button1: string, button2: string) => {
    const searchButtonRegex = new RegExp(`^${button1}`);
    cy.get('[role="dialog"]').contains('button', searchButtonRegex).should('be.visible');
    cy.get('[role="dialog"]').contains('button', button2).should('be.visible');
  },
  selectMenuItemInModal: (itemName: string) => {
    cy.get('[role="dialog"]')
      .contains('[role="menuitem"]', itemName)
      .click();
  },
  selectMatchGroupInModal: (groupName: string) => {
    cy.getDataIdCy({ idAlias: 'search-all-files-dialog__content-match-group-select' }).click();
    cy.get('[role="option"]').contains(groupName).click();
  },
  clickVideoWidgetButton: (buttonName: string) => {
    cy.get('.video-react-control-bar').contains('button', buttonName).click();
  },

  seekVideoProgressBar: () => {
    cy.get('.video-react-progress-holder').click('center');
  },

  changePlaybackSpeed: (speed: string) => {
    cy.get('.video-react-playback-rate').click();
    cy.get('.video-react-menu-item').contains(speed).click({ force: true });
  },

  verifyPlaybackSpeed: (speed: string) => {
    cy.get('.video-react-playback-rate-value').should('have.text', speed);
  },

  verifyFullscreenMode: () => {
    cy.get('.video-react-fullscreen-control').contains('Non-Fullscreen');
  },
  verifyAccordionState: (accordionName: string, state: 'expanded' | 'collapsed') => {
    const expectedState = state === 'expanded' ? 'true' : 'false';
    cy.contains('[role="button"]', accordionName)
      .should('have.attr', 'aria-expanded', expectedState);
  },

  clickAccordionHeader: (accordionName: string) => {
    cy.contains('[role="button"]', accordionName).click();
  },
  verifyFileMetadata: (label: string, value: string) => {
    cy.contains('li', `${label}:`)
      .contains('div', value)
      .should('be.visible');
  },
  verifyThumbnailScalerIsVisible: () => {
    cy.getDataIdCy({ idAlias: 'thumbnail-scaler' }).should('be.visible');
  },

  changeResultsPerPage: (count: string) => {
    cy.get('.pagination-page-size-select').click();
    cy.get(`[role="option"][data-value="${count}"]`).click();
  },

  verifyResultsPerPageValue: (value: string) => {
    cy.getDataIdCy({ idAlias: 'table-pagination-page-size' })
      .should('have.value', value);
  },
};
