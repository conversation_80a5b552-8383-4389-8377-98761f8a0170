import { Before, Then, When, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { matchGroupPage } from '../../../pages/matchGroupPage';
import { eventDetailsPage } from '../../../pages/eventDetailsPage';
import { MatchGroupButton, MatchGroupTab, MatchGroupDataTestId } from '../../../support/helperFunction/matchGroupHelper';
import '../activeTab/activeTab.step';
import '../event-details/event-details.step';
import '../main-page/main-page.steps';
import '../event-screen/event-screen.step';

Before({ tags: '@match-group' }, () => {
  cy.LoginLandingPage();
});

When('The user clicks {string} for the first match group', (action: string) => {
  if (action === MatchGroupButton.VIEW_MATCH_GROUP) {
    matchGroupPage.clickFirstViewMatchGroupButton();
  }
});

When('The user clicks the {string} tab in the match group page', (tabName: string) => {
  matchGroupPage.clickTab(tabName);
});

When('The user selects {string} from the dropdown', (selection: string) => {
  matchGroupPage.selectPotentialMatchSearch(selection);
});

Then('The user should see the match group page UI', () => {
  matchGroupPage.verifyMatchGroupPageUI();
});

Then('The user should see the breadcrumb with event name {string} and group name', (eventName: string) => {
  cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_BREADCRUMB }).invoke('text').then((groupName: string) => {
    matchGroupPage.verifyBreadcrumb(eventName, groupName);
  });
});

Then('The user should see the File Name dropdown and Export button', () => {
  matchGroupPage.verifyFileNameDropdownAndExportButton();
});

Then('The user should see {string} on the left screen', (text: string) => {
  if (text === 'Select a Detection to View Details') {
    matchGroupPage.verifyLeftScreenContent();
  }
});

Then('The user should see the right screen tabs {string}, {string}, and {string}', (_tab1: string, _tab2: string, _tab3: string) => {
  matchGroupPage.verifyRightScreenTabs();
});

Then('The {string} tab should be selected by default', (tabName: string) => {
  if (tabName === MatchGroupTab.VERIFIED_MATCHES) {
    matchGroupPage.verifyDefaultTab();
  }
});

When('The user hovers on a match group search in the match group page', () => {
  matchGroupPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search in the match group page', () => {
  matchGroupPage.clickFirstEditIcon();
});

When('The user enters {string} as the new search name in the match group page', (newName: string) => {
  matchGroupPage.enterNewSearchName(newName);
});

When('The user clicks {string} in the match group page dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    matchGroupPage.clickSaveInDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    matchGroupPage.clickDeleteInDialog();
  }
});

Then('The search name should be updated to {string} in the match group page', (newName: string) => {
  matchGroupPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search in the match group page', () => {
  matchGroupPage.clickFirstDeleteIcon();
});

Then('The match group search should be deleted successfully in the match group page', () => {
  cy.get('[data-testid^="match-group-row-"]').should('have.length.lessThan', 2);
});

When('The user clicks on the 3 dots button in the match group page', () => {
  matchGroupPage.clickThreeDotsButton();
});

When('The user selects {string} from the match group page menu', (option: string) => {
  if (option === 'Rename') {
    matchGroupPage.selectRename();
  } else if (option === 'Delete') {
    matchGroupPage.selectDelete();
  }
});

When('The user enters {string} as the new match group name in the match group page', (newName: string) => {
  matchGroupPage.enterNewMatchGroupName(newName);
});

Then('The match group name should be updated to {string} in the match group page', (newName: string) => {
  matchGroupPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The confirmation modal should display the delete message in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The modal should have Cancel and Delete buttons in the match group page', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

When('The user clicks {string} in the match group page confirmation', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    matchGroupPage.clickYesDelete();
  }
});

Then('The match group should be deleted successfully from the match group page', () => {
  matchGroupPage.verifyMatchGroupDeleted();
});

When('The user expands the first match group', () => {
  eventDetailsPage.expandFirstMatchGroup();
});

When('The user hovers on a match group search', () => {
  eventDetailsPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search', () => {
  eventDetailsPage.clickFirstSearchEditIcon();
});

When('The user enters {string} as the new search name', (newName: string) => {
  eventDetailsPage.enterNewSearchNameInDialog(newName);
});

When('The user clicks {string} in the search dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveOrDeleteInSearchDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    eventDetailsPage.clickSaveOrDeleteInSearchDialog();
  } else if (buttonText === MatchGroupButton.CANCEL) {
    eventDetailsPage.clickCancelInSearchDialog();
  }
});

When('The user clicks the {string} button in the dialog', (buttonText: string) => {
  eventDetailsPage.clickButtonInDialog(buttonText);
});

Then('The search name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search', () => {
  eventDetailsPage.clickFirstSearchDeleteIcon();
});

Then('The match group search should be deleted successfully', () => {
  eventDetailsPage.verifySearchDeleted();
});

When('The user clicks on the 3 dots button for the first match group', () => {
  eventDetailsPage.clickThreeDotsButtonForFirstMatchGroup();
});

When('The user selects {string}', (option: string) => {
  if (option === 'Rename') {
    eventDetailsPage.selectRenameFromMenu();
  } else if (option === 'Delete') {
    eventDetailsPage.selectDeleteFromMenu();
  }
});

When('The user enters {string} as the new name', (newName: string) => {
  eventDetailsPage.enterNewMatchGroupNameInDialog(newName);
});

When('The user clicks {string} in the match group dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveInMatchGroupDialog();
  }
});

Then('The match group name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal', () => {
  eventDetailsPage.verifyMatchGroupSearchDeleteConfirmationModal();
});

Then('The confirmation modal should display the message {string}', (message: string) => {
  eventDetailsPage.verifyMatchGroupDeleteConfirmationModal(message);
});

When('The user clicks {string}', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    eventDetailsPage.clickYesDeleteInMatchGroupDialog();
  }
});

Then('The match group should be deleted successfully', () => {
  eventDetailsPage.verifyMatchGroupDeleted();
});

Then('The user should see the following match group UI elements:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as Array<{ 'Element Type': string; 'Expected Value': string }>;

  rows.forEach((row) => {
    const elementType = row['Element Type'];
    const expectedValue = row['Expected Value'];

    switch (elementType) {
    case 'Breadcrumb':
      cy.get('[data-testid="match-group-breadcrumb"]').invoke('text').then((groupName: string) => {
        matchGroupPage.verifyBreadcrumb(expectedValue, groupName, 'Potential Match Search 1');
      });
      break;
    case 'File Name Dropdown':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Export Button':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Left Panel Text':
      if (expectedValue === 'Select a Detection to View Details') {
        matchGroupPage.verifyLeftScreenContent();
      }
      break;
    case 'Tab 1':
    case 'Tab 2':
    case 'Tab 3':
      matchGroupPage.verifyRightScreenTabs();
      break;
    case 'Default Tab':
      if (expectedValue === 'Search Results') {
        matchGroupPage.verifyDefaultTab();
      }
      break;
    default:
      throw new Error(`Unknown element type: ${elementType}`);
    }
  });
});

When('The user clicks the {string} button for the first search', (buttonText: string) => {
  eventDetailsPage.clickFirstViewSearchButton(buttonText);
});

Then('The user should navigate to the match search view page', () => {
  matchGroupPage.verifyMatchSearchViewPage();
});

Then('The user should see that the page has finished loading', () => {
  matchGroupPage.verifyPageIsLoaded();
});

Then('The user should see the following elements on the Search Results page:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as Array<{ 'Element Type': string; 'Expected State': string }>;

  rows.forEach((row) => {
    const elementType = row['Element Type'];
    const expectedState = row['Expected State'];

    switch (elementType) {
    case 'Breadcrumb':
      matchGroupPage.verifyBreadcrumbIsVisible();
      break;
    case 'File Name Dropdown':
      matchGroupPage.verifyFileNameDropdown(expectedState);
      break;
    case 'Mark as Verified Button':
      matchGroupPage.verifyMarkAsVerifiedButton(expectedState);
      break;
    case 'Find Matches Button':
      matchGroupPage.verifyFindMatchesButton(expectedState);
      break;
    case 'Potential Match Search Dropdown':
      matchGroupPage.verifyPotentialMatchSearchDropdown(expectedState);
      break;
    case 'Media Player Default Text':
      matchGroupPage.verifyMediaPlayerDefaultText(expectedState);
      break;
    case 'Select All Checkbox':
      matchGroupPage.verifySelectAllCheckbox(expectedState);
      break;
    case 'Confidence Slider':
      matchGroupPage.verifyConfidenceSlider(expectedState);
      break;
    case 'Show Match Score Checkbox':
      matchGroupPage.verifyShowMatchScoreCheckbox(expectedState);
      break;
    case 'Pagination':
      matchGroupPage.verifyPagination(expectedState);
      break;
    case 'Thumbnail Scaler':
      matchGroupPage.verifyThumbnailScaler(expectedState);
      break;
    default:
      throw new Error(`Unknown element type: ${elementType}`);
    }
  });
});

const getIndexFromOrdinal = (ordinal: string): number => {
  const numberPart = parseInt(ordinal, 10);
  if (isNaN(numberPart)) {
    throw new Error(`Could not parse number from ordinal string: ${ordinal}`);
  }
  return numberPart - 1;
};

When('The user selects the {string} tracklet', (ordinal: string) => {
  const index = getIndexFromOrdinal(ordinal);
  matchGroupPage.selectTrackletByIndex(index);
});

When('The user clicks the {string} button in the match group page', (_buttonText: string) => {
  matchGroupPage.clickFindMatchesButton();
});

Then('The {string} popover should be visible', (popoverName: string) => {
  if (popoverName === 'Find Matches') {
    matchGroupPage.verifyFindMatchesPopoverIsVisible();
  }
});

When('The user clicks the {string} button in the popover', (buttonText: string) => {
  if (buttonText === 'New Match Group') {
    matchGroupPage.clickNewMatchGroupInPopover();
  } else if (buttonText === 'Continue') {
    matchGroupPage.clickContinueInPopover();
  }
});

When('The user enters {string} as the new match group name', (name: string) => {
  matchGroupPage.enterNewMatchGroupNameInPopover(name);
});

When('The user clicks the create button for the new match group', () => {
  matchGroupPage.clickCreateNewMatchGroupInPopover();
});

Then('The user should see the success notification {string}', (message: string) => {
  matchGroupPage.verifySuccessNotification(message);
});

Then('The match group named {string} should be visible', (name: string) => {
  eventDetailsPage.verifyMatchGroupExists(name);
});

When('The user clicks the 3 dots menu for the {string} match group', (ordinal: string) => {
  const index = getIndexFromOrdinal(ordinal);
  eventDetailsPage.clickThreeDotsMenuForMatchGroupByIndex(index);
});

When('The user clicks the {string} button for the new match group', (_buttonText: string) => {
  matchGroupPage.clickCreateNewMatchGroupInPopover();
});

When('The user clicks on the {string} tracklet', (ordinal: string) => {
  const index = getIndexFromOrdinal(ordinal);
  matchGroupPage.clickOnTrackletByIndex(index);
});

Then('The {string} accordion should be visible', (accordionName: string) => {
  matchGroupPage.verifyAccordionIsVisible(accordionName);
});

When('The user selects the checkbox for the {string} tracklet', (ordinal: string) => {
  const index = getIndexFromOrdinal(ordinal);
  matchGroupPage.selectTrackletCheckboxByIndex(index);
});

Then('The {string} tracklet should have a blue outline', (ordinal:string) => {
  const index = getIndexFromOrdinal(ordinal);
  matchGroupPage.verifyTrackletHasBlueOutline(index);
});

Then('The video player state should be {string}', (state: 'playing' | 'paused') => {
  matchGroupPage.verifyVideoPlayerState(state);
});

When('The user clicks the video player {string} button', (buttonName: 'Play' | 'Pause') => {
  matchGroupPage.clickVideoPlayerButton(buttonName);
});

Then('the confidence slider should be visible', () => {
  matchGroupPage.verifyConfidenceSliderIsVisible();
});

When('The user clicks the select all checkbox', () => {
  matchGroupPage.clickSelectAllCheckbox();
});

Then('all tracklets on the page should be selected', () => {
  matchGroupPage.verifyAllTrackletsAreSelected();
});

When('The user clicks the {string} checkbox', (checkboxLabel: string) => {
  matchGroupPage.clickCheckboxByLabel(checkboxLabel);
});

Then('all visible tracklets should display a match score', () => {
  matchGroupPage.verifyAllTrackletsShowConfidenceScore();
});

Then('The {string} button should be {string}', (buttonName: string, state: 'enabled' | 'disabled') => {
  if (buttonName === 'Find Matches') {
    matchGroupPage.verifyFindMatchesButtonState(state);
  }
});

When('The user filters by the file named {string}', (fileName: string) => {
  matchGroupPage.filterByFileName(fileName);
});

Then('all visible tracklets should be from the same source file', () => {
  matchGroupPage.verifyAllTrackletsAreFromSameFile();
});

When('The user selects {string} from the potential match search dropdown', (searchName: string) => {
  matchGroupPage.selectFromPotentialMatchSearchDropdown(searchName);
});

Then('{string} should be the selected search', (searchName: string) => {
  matchGroupPage.verifySelectedPotentialMatchSearch(searchName);
});

Then('The modal should contain {string} and {string} options', (option1: string, option2: string) => {
  matchGroupPage.verifySearchModalContainsOptions(option1, option2);
});

Then('The modal should contain {string} and {string} buttons', (button1: string, button2: string) => {
  matchGroupPage.verifySearchModalContainsButtons(button1, button2);
});

When('The user selects the {string} attribute', (attributeName: string) => {
  matchGroupPage.selectMenuItemInModal(attributeName);
});

When('The user selects the {string} match group', (groupName: string) => {
  matchGroupPage.selectMatchGroupInModal(groupName);
});

When('The user clicks the delete icon for the {string}', (searchName: string) => {
  eventDetailsPage.clickDeleteIconForSearch(searchName);
});

Then('The {string} should no longer be visible', (searchName: string) => {
  eventDetailsPage.verifySearchIsNoLongerVisible(searchName);
});

When('The user clicks the {string} widget button', (buttonName: string) => {
  matchGroupPage.clickVideoWidgetButton(buttonName);
});

When('The user seeks to the middle of the video progress bar', () => {
  matchGroupPage.seekVideoProgressBar();
});

When('The user changes the video playback speed to {string}', (speed: string) => {
  matchGroupPage.changePlaybackSpeed(speed);
});

Then('The video playback speed should be {string}', (speed: string) => {
  matchGroupPage.verifyPlaybackSpeed(speed);
});

Then('The video player should be in fullscreen mode', () => {
  matchGroupPage.verifyFullscreenMode();
});

Then('The {string} accordion should be {string}', (accordionName: string, state: 'expanded' | 'collapsed') => {
  matchGroupPage.verifyAccordionState(accordionName, state);
});

When('The user clicks the {string} accordion header', (accordionName: string) => {
  matchGroupPage.clickAccordionHeader(accordionName);
});

Then('The following file metadata should be visible:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as Array<{ 'Label': string; 'Value': string }>;

  rows.forEach((row) => {
    matchGroupPage.verifyFileMetadata(row.Label, row.Value);
  });
});

Then('The thumbnail scaler widget should be visible', () => {
  matchGroupPage.verifyThumbnailScalerIsVisible();
});

When('The user changes the results per page to {string}', (count: string) => {
  matchGroupPage.changeResultsPerPage(count);
});

Then('the {string} value should be {string}', (label: string, value: string) => {
  if (label === 'Results Per Page') {
    matchGroupPage.verifyResultsPerPageValue(value);
  }
});
